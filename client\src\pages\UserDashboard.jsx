import { useState, useEffect, useContext } from 'react';
import { AppContent } from '../context/AppContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import Navbar from '../components/Navbar';
import ImageUpload from '../components/ImageUpload';
import UploadCard from '../components/UploadCard';



const UserDashboard = () => {
    const { backendUrl, userData } = useContext(AppContent);
    const [uploads, setUploads] = useState([]);
    const [reports, setReports] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('upload');

    useEffect(() => {
        fetchUserData();
    }, []);

    const fetchUserData = async () => {
        try {
            setLoading(true);
            
            // Fetch uploads
            const uploadsResponse = await axios.get(`${backendUrl}/api/upload/user`);
            if (uploadsResponse.data.success) {
                setUploads(uploadsResponse.data.uploads);
            }

            // Fetch reports
            const reportsResponse = await axios.get(`${backendUrl}/api/report/user/all`);
            if (reportsResponse.data.success) {
                setReports(reportsResponse.data.reports);
            }

        } catch (error) {
            toast.error('Error fetching data');
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const handleUploadSuccess = () => {
        fetchUserData();
        // Stay on upload tab to show analysis results
        // User can navigate to history or reports manually if needed
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <Navbar />
                <div className="flex justify-center items-center h-96">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <Navbar />
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Account Verification Notice */}
                {userData && !userData.isAccountVerified && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-yellow-800">
                                    Account Verification Pending
                                </h3>
                                <div className="mt-2 text-sm text-yellow-700">
                                    <p>
                                        Your account is not yet verified. You can still use all features, but we recommend verifying your email for enhanced security.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Welcome Section */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Welcome back, {userData?.name}!
                    </h1>
                    <p className="text-gray-600">
                        Upload your MRI scans for AI-powered brain tumor analysis
                    </p>
                </div>



                {/* Tab Navigation */}
                <div className="bg-white rounded-lg shadow-sm mb-8">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8 px-6">
                            <button
                                onClick={() => setActiveTab('upload')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'upload'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Upload MRI
                            </button>
                            <button
                                onClick={() => setActiveTab('history')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'history'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Upload History
                            </button>

                        </nav>
                    </div>

                    <div className="p-6">
                        {activeTab === 'upload' && (
                            <div className="space-y-6">
                                <ImageUpload onUploadSuccess={handleUploadSuccess} />
                            </div>
                        )}

                        {activeTab === 'history' && (
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Upload History</h3>
                                {uploads.length === 0 ? (
                                    <p className="text-gray-500 text-center py-8">No uploads yet</p>
                                ) : (
                                    uploads.map((upload) => (
                                        <UploadCard
                                            key={upload._id}
                                            upload={upload}
                                            onUpdate={fetchUserData}
                                        />
                                    ))
                                )}
                            </div>
                        )}


                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserDashboard;
